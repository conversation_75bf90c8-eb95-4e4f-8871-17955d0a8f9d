import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import * as audioUtils from '../utils/audioStreamingStatic';
import { AgentRoleEnum } from '../types/enums';
import { WS_CONFIG, AGENT_ROLES, ERROR_MESSAGES, AUDIO_CONFIG } from '../config/appConfig';
import { useBrowserCompatibility } from '../context/BrowserCompatibilityContext';
import { useAuth } from '../context/ApiAuthContext';

// Reusable handler functions for assistant components
export const assistantHandlers = {
  /**
   * Handles mic button click across all assistant components - PURE MUTE/UNMUTE ONLY
   * @param assistantRef - Reference to the AssistantChat component
   * @param isRecording - Current recording state
   * @param isUnmountingRef - Reference to track if component is unmounting
   * @param componentName - Name of the component for logging
   * @param onRecordingStateChange - Callback to notify about recording state changes
   * @returns Promise<void>
   */
  handleMicClick: async (
    assistantRef: React.RefObject<any>,
    isRecording: boolean,
    isUnmountingRef: React.RefObject<boolean>,
    componentName: string,
    onRecordingStateChange?: (isRecording: boolean) => void
  ): Promise<void> => {
    if (!assistantRef.current) return;

    // Check if component is unmounting
    if (isUnmountingRef.current) {
      console.log(`${componentName}: Component is unmounting, ignoring mic click`);
      return;
    }

    // Check WebSocket connection state
    const socket = (window as any).assistantSocket;
    const isSocketConnected = socket && socket.readyState === WebSocket.OPEN;

    if (!isSocketConnected) {
      console.log(`${componentName}: WebSocket not connected, cannot toggle microphone`);
      alert('Please connect to the assistant first before using the microphone.');
      return;
    }

    if (isRecording) {
      // If recording, mute microphone - LIGHTWEIGHT OPERATION
      console.log(`${componentName}: Muting microphone (stopping audio transmission)`);
      try {
        audioUtils.muteMicrophone();
        // Notify parent component about recording state change
        if (onRecordingStateChange) {
          onRecordingStateChange(false);
        }
      } catch (err) {
        console.error(`${componentName}: Failed to mute microphone:`, err);
      }
    } else {
      // If not recording, unmute microphone - LIGHTWEIGHT OPERATION
      console.log(`${componentName}: Unmuting microphone (resuming audio transmission)`);
      try {
        audioUtils.unmuteMicrophone();
        // Notify parent component about recording state change
        if (onRecordingStateChange) {
          onRecordingStateChange(true);
        }
      } catch (err) {
        console.error(`${componentName}: Failed to unmute microphone:`, err);
        alert('Failed to unmute microphone. Please try again.');
      }
    }
  },

  /**
   * Handles stop button click across all assistant components
   * @param assistantRef - Reference to the AssistantChat component
   * @param isPaused - Current paused state
   * @param isUnmountingRef - Reference to track if component is unmounting
   * @param componentName - Name of the component for logging
   * @param setIsPaused - Function to update the paused state
   * @param onStopCallback - Optional callback to run after stopping
   */
  handleStopClick: (
    assistantRef: React.RefObject<any>,
    isPaused: boolean,
    isUnmountingRef: React.RefObject<boolean>,
    componentName: string,
    setIsPaused: (isPaused: boolean) => void,
    onStopCallback?: () => void
  ): void => {
    // Check if component is unmounting
    if (isUnmountingRef.current) {
      console.log(`${componentName}: Component is unmounting, ignoring stop click`);
      return;
    }

    if (!assistantRef.current) return;

    if (isPaused) {
      // If already paused, fully end the conversation
      console.log(`${componentName}: Conversation already paused, ending it completely`);

      // Set the unmounting flag to prevent new connections
      isUnmountingRef.current = true;

      // Set the unmounting flag in audioUtils to prevent new operations
      audioUtils.setUnmountingFlag(true);

      // End the conversation safely
      try {
        if (assistantRef.current && typeof assistantRef.current.endConversation === 'function') {
          assistantRef.current.endConversation();
        }

        // Force close any remaining connections
        if (typeof window !== 'undefined' && window.assistantSocket) {
          try {
            console.log(`${componentName}: Force closing any remaining WebSocket connections`);
            window.assistantSocket.close(1000, 'User ended conversation');
            window.assistantSocket = undefined;
          } catch (err) {
            console.log(`${componentName}: Error closing global WebSocket, continuing:`, err);
          }
        }
      } catch (err) {
        console.log(`${componentName}: Error while ending conversation, continuing:`, err);
      }

      setIsPaused(false);

      // Run callback if provided
      if (onStopCallback) onStopCallback();
    } else {
      // Pause the conversation instead of ending it
      console.log(`${componentName}: Pausing conversation`);

      // Pause the conversation
      assistantRef.current.pauseConversation();
      setIsPaused(true);

      // Run callback if provided
      if (onStopCallback) onStopCallback();
    }
  },

  /**
   * Handles reconnect button click across all assistant components
   * @param assistantRef - Reference to the AssistantChat component
   * @param isPaused - Current paused state
   * @param isUnmountingRef - Reference to track if component is unmounting
   * @param componentName - Name of the component for logging
   * @param setIsPaused - Function to update the paused state
   */
  handleReconnectClick: async (
    assistantRef: React.RefObject<any>,
    isPaused: boolean,
    isUnmountingRef: React.RefObject<boolean>,
    componentName: string,
    setIsPaused: (isPaused: boolean) => void
  ): Promise<void> => {
    // Check if component is unmounting
    if (isUnmountingRef.current) {
      return;
    }

    if (!assistantRef.current) return;

    if (isPaused) {
      // If paused, resume the conversation and start recording
      console.log(`${componentName}: Resuming paused conversation and starting microphone from reconnect click`);
      try {
        // First resume the WebSocket connection
        await assistantRef.current.resumeConversation();
        console.log(`${componentName}: Conversation resumed successfully from reconnect click`);
        setIsPaused(false);

        // Clear any remaining audio in the queue to ensure clean state
        audioUtils.clearAudioQueue();

        // Always start recording after resuming from pause when reconnect is clicked
        // Add a small delay to ensure WebSocket is fully ready
        setTimeout(async () => {
          if (assistantRef.current && !isUnmountingRef.current) {
            console.log(`${componentName}: Starting recording after resuming from reconnect click`);
            try {
              // BEFORE starting recording, ensure first message state is set for reconnection
              audioUtils.resetConnectionState(true); // Reset with reconnection flag FIRST
              console.log(`${componentName}: First message state bypassed for reconnection - recording will be enabled`);

              await assistantRef.current.startRecording();

              // Clear reconnection mode flag after successful recording start
              audioUtils.clearReconnectionMode();
              console.log(`${componentName}: Recording started successfully after reconnection`);
            } catch (err) {
              console.error(`${componentName}: Failed to start recording after reconnect click:`, err);
              alert('Failed to start recording. Please try again.');
            }
          }
        }, AGENT_ROLES.RECORDING_DELAY);
      } catch (err) {
        console.error(`${componentName}: Failed to resume conversation from reconnect click:`, err);
        alert('Failed to resume. Please try again.');
      }
    } else {
      // Otherwise, force reconnect with proper cleanup
      try {
        console.log(`${componentName}: Force reconnecting WebSocket connection`);

        // First, force close the existing connection and reset state
        audioUtils.forceReconnect();
        audioUtils.resetConnectionState(true); // Pass true to indicate this is a reconnection

        // Reset connection state flags to allow reconnection
        if (assistantRef.current) {
          // Reset any intentional closing flags that might prevent reconnection
          try {
            // Access the internal state if possible
            const assistantInternal = assistantRef.current as any;
            if (assistantInternal.isIntentionallyClosingRef) {
              assistantInternal.isIntentionallyClosingRef.current = false;
            }
          } catch (err) {
            console.log(`${componentName}: Could not reset internal flags:`, err);
          }
        }

        // Clean up current connection without setting intentional closing flags
        if (assistantRef.current && assistantRef.current.assistantSocket) {
          try {
            const socket = assistantRef.current.assistantSocket;
            if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
              socket.close(1000, 'Reconnecting');
            }
          } catch (err) {
            console.log(`${componentName}: Error closing socket for reconnect:`, err);
          }
        }

        // Wait a moment for cleanup to complete
        await new Promise(resolve => setTimeout(resolve, 300));

        // Now start a fresh connection
        await assistantRef.current.startConnection();
        console.log(`${componentName}: Reconnection completed successfully`);
      } catch (err) {
        console.error(`${componentName}: Reconnection failed from button click:`, err);
        alert('Reconnection failed. Please refresh the page and try again.');
      }
    }
  },

  /**
   * Handles play button click across all assistant components
   * @param assistantRef - Reference to the AssistantChat component
   * @param isPaused - Current paused state
   * @param isUnmountingRef - Reference to track if component is unmounting
   * @param componentName - Name of the component for logging
   * @param setIsPaused - Function to update the paused state
   */
  handlePlayClick: async (
    assistantRef: React.RefObject<any>,
    isPaused: boolean,
    isUnmountingRef: React.RefObject<boolean>,
    componentName: string,
    setIsPaused: (isPaused: boolean) => void
  ): Promise<void> => {
    // Check if component is unmounting
    if (isUnmountingRef.current) {
      console.log(`${componentName}: Component is unmounting, ignoring play click`);
      return;
    }

    if (!assistantRef.current) return;

    if (isPaused) {
      // If paused, resume the conversation and start recording
      console.log(`${componentName}: Resuming paused conversation and starting microphone from play click`);
      try {
        // First resume the WebSocket connection
        await assistantRef.current.resumeConversation();
        console.log(`${componentName}: Conversation resumed successfully from play click`);
        setIsPaused(false);

        // Clear any remaining audio in the queue to ensure clean state
        audioUtils.clearAudioQueue();

        // Always start recording after resuming from pause when play button is clicked
        // Add a small delay to ensure WebSocket is fully ready
        setTimeout(async () => {
          if (assistantRef.current && !isUnmountingRef.current) {
            console.log(`${componentName}: Starting recording after resuming from play click`);
            try {
              // BEFORE starting recording, ensure first message state is set for reconnection
              audioUtils.resetConnectionState(true); // Reset with reconnection flag FIRST
              console.log(`${componentName}: First message state bypassed for reconnection - recording will be enabled`);

              await assistantRef.current.startRecording();

              // Clear reconnection mode flag after successful recording start
              audioUtils.clearReconnectionMode();
              console.log(`${componentName}: Recording started successfully after reconnection`);
            } catch (err) {
              console.error(`${componentName}: Failed to start recording after play click:`, err);
              alert('Failed to start recording. Please try again.');
            }
          }
        }, AGENT_ROLES.RECORDING_DELAY);
      } catch (err) {
        console.error(`${componentName}: Failed to resume conversation from play click:`, err);
        alert('Failed to resume. Please try again.');
      }
    } else {
      // Otherwise, force reconnect with proper cleanup
      try {
        console.log(`${componentName}: Force reconnecting WebSocket connection from play click`);

        // First, force close the existing connection and reset state
        audioUtils.forceReconnect();
        audioUtils.resetConnectionState(true); // Pass true to indicate this is a reconnection

        // Reset connection state flags to allow reconnection
        if (assistantRef.current) {
          // Reset any intentional closing flags that might prevent reconnection
          try {
            // Access the internal state if possible
            const assistantInternal = assistantRef.current as any;
            if (assistantInternal.isIntentionallyClosingRef) {
              assistantInternal.isIntentionallyClosingRef.current = false;
            }
          } catch (err) {
            console.log(`${componentName}: Could not reset internal flags from play click:`, err);
          }
        }

        // Clean up current connection without setting intentional closing flags
        if (assistantRef.current && assistantRef.current.assistantSocket) {
          try {
            const socket = assistantRef.current.assistantSocket;
            if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
              socket.close(1000, 'Reconnecting');
            }
          } catch (err) {
            console.log(`${componentName}: Error closing socket for reconnect from play click:`, err);
          }
        }

        // Wait a moment for cleanup to complete
        await new Promise(resolve => setTimeout(resolve, 300));

        // Now start a fresh connection
        await assistantRef.current.startConnection();
        console.log(`${componentName}: Reconnection from play click completed successfully`);
      } catch (err) {
        console.error(`${componentName}: Reconnection failed from play click:`, err);
        alert('Reconnection failed. Please refresh the page and try again.');
      }
    }
  }
};

interface AssistantChatProps {
  userToken: string;
  avatarGender: 'male' | 'female';
  agentRole?: AgentRoleEnum; // Use the AgentRoleEnum type
  language?: string; // Language parameter for WebSocket connection
  onConversationComplete?: () => void;
  onRecordingStateChange?: (isRecording: boolean) => void;
  onConnectionStateChange?: (isConnected: boolean) => void;
}

export interface AssistantHandle {
  startConnection: () => Promise<void>;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  handleInterruption: () => void;
  endConversation: () => void;
  pauseConversation: () => void;
  resumeConversation: () => Promise<void>;
  assistantSocket: WebSocket | null;
  isPaused: boolean;
}

declare global {
  interface Window {
    assistantSocket?: WebSocket;
  }
}

const AssistantChat = forwardRef<AssistantHandle, AssistantChatProps>(
  ({ userToken, avatarGender, agentRole = AgentRoleEnum.OBA, language = 'en', onConversationComplete, onRecordingStateChange, onConnectionStateChange }, ref) => {
    // Get browser compatibility information from context
    const {
      isBrowserCompatible,
      hasMicrophonePermission,
      hasWebSocketSupport,
      hasAudioContextSupport,
      checkMicrophonePermission
    } = useBrowserCompatibility();

    // Get user profile to use language from it if available
    const { user } = useAuth();

    // Error state is used for setting error messages that are passed to parent components
    const [error, setError] = useState<string | null>(null);
    const socketRef = useRef<WebSocket | null>(null);
    const reconnectAttemptsRef = useRef<number>(0);
    const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    // isConnected state is used to track connection status and notify parent components
    const [isConnected, setIsConnected] = useState<boolean>(false);
    const isIntentionallyClosingRef = useRef<boolean>(false);
    // isPaused state is used to track if the WebSocket is paused (not fully closed)
    const [isPaused, setIsPaused] = useState<boolean>(false);

    // Function to append messages (production-ready)
    const appendMessage = (sender: string, message: string) => {
      // Message handling for production
    };

    // Connect to WebSocket with retry logic
    const connectWebSocket = async () => {
      // First check browser compatibility from context
      if (!isBrowserCompatible || !hasWebSocketSupport) {
        console.error('Browser is not compatible with required features');
        setError('Your browser does not support all required features. Some functionality may be limited.');
        setIsConnected(false);
        if (onConnectionStateChange) onConnectionStateChange(false);
        return;
      }

      // We'll check microphone permission only when we actually need to record
      // Not during initial connection

      // Clear any existing reconnect timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      // Close existing connection if any
      if (socketRef.current) {
        try {
          socketRef.current.close();
        } catch (err) {
          console.warn('Error closing existing WebSocket:', err);
        }
      }

      try {
        // Use language from user profile if available, otherwise use the provided language
        const userLanguage = user?.details?.language || language;

        // Log the language being used for debugging
        console.log(`Using language for WebSocket: ${userLanguage} (from user profile: ${user?.details?.language}, from props: ${language})`);

        // Create new WebSocket connection with the provided agent_role and language
        // Ensure the WebSocket URL uses the ws:// or wss:// protocol
        let wsUrl = `${WS_CONFIG.BASE_URL}/ws?token=${encodeURIComponent(userToken)}&agent_role=${agentRole}&language=${userLanguage}`;

        // Convert http:// to ws:// and https:// to wss:// if needed
        if (wsUrl.startsWith('http://')) {
          wsUrl = wsUrl.replace('http://', 'ws://');
          console.log('Converted WebSocket URL from http:// to ws://');
        } else if (wsUrl.startsWith('https://')) {
          wsUrl = wsUrl.replace('https://', 'wss://');
          console.log('Converted WebSocket URL from https:// to wss://');
        }

        console.log('Connecting to WebSocket with agent_role:', agentRole);
        console.log('WebSocket URL:', wsUrl);

        const socket = new WebSocket(wsUrl);

        socket.onopen = async () => {
          console.log('WebSocket connection established');
          window.assistantSocket = socket;
          reconnectAttemptsRef.current = 0; // Reset reconnect attempts on successful connection
          setIsConnected(true);
          if (onConnectionStateChange) onConnectionStateChange(true);
          setError(null);

          // Store the socket reference immediately
          socketRef.current = socket;

          // Set the WebSocket in the audio utils first, before any operations
          try {
            audioUtils.setWebSocket(socket);
            console.log('WebSocket set in audioUtils successfully');
          } catch (wsErr) {
            console.error('Error setting WebSocket in audioUtils:', wsErr);
          }

          // Add a small delay to ensure WebSocket is fully ready
          await new Promise(resolve => setTimeout(resolve, 100));

          // No longer sending automatic initial greeting message
          // The conversation will start when the user begins speaking or interacting

          // Add another small delay before starting recording
          await new Promise(resolve => setTimeout(resolve, 200));

          // Auto-start recording if configured, but handle PPCA differently
          if (AUDIO_CONFIG.AUTO_START_RECORDING) {
            try {
              // For PPCA conversations, set up audio pipeline but don't start recording yet
              // The recording will be enabled after the first assistant message
              if (agentRole === AgentRoleEnum.PPCA) {
                console.log('PPCA conversation detected - setting up audio pipeline but delaying recording until after first assistant message');

                // Initialize audio context first (doesn't require permissions)
                await audioUtils.initAudioContext();
                console.log('Audio context initialized for PPCA conversation');

                // Check microphone permission and set up pipeline
                const hasMicPermission = await checkMicrophonePermission();

                if (hasMicPermission) {
                  // Set up the audio pipeline but keep recording muted during first message
                  if (socket.readyState === WebSocket.OPEN) {
                    await audioUtils.startRecording(); // This will set up pipeline but keep isRecording=false for first message
                    console.log('PPCA audio pipeline set up successfully (recording muted until first assistant message completes)');
                    // Don't call onRecordingStateChange(true) yet - wait for first message to complete
                  }
                } else {
                  console.log('Microphone permission not granted for PPCA, continuing without recording');
                }
              } else {
                // For non-PPCA conversations, start recording immediately as before
                console.log('Non-PPCA conversation - auto-starting recording immediately...');

                // Verify WebSocket is still connected before starting recording
                if (socket.readyState !== WebSocket.OPEN) {
                  console.error('WebSocket not in OPEN state before starting recording');
                  return;
                }

                // Add a small delay before starting recording
                await new Promise(resolve => setTimeout(resolve, AGENT_ROLES.RECORDING_DELAY));

                try {
                  // Initialize audio context first (doesn't require permissions)
                  await audioUtils.initAudioContext();
                  console.log('Audio context initialized successfully');

                  // Verify WebSocket is still connected after audio context initialization
                  if (socket.readyState !== WebSocket.OPEN) {
                    console.error('WebSocket not in OPEN state after audio context initialization');
                    return;
                  }

                  // Only now check microphone permission and start recording
                  // This will only prompt for permission when we actually need to record
                  const hasMicPermission = await checkMicrophonePermission();

                  if (hasMicPermission) {
                    // Final check that WebSocket is still connected
                    if (socket.readyState === WebSocket.OPEN) {
                      await audioUtils.startRecording();
                      console.log('Auto-recording started successfully');
                      if (onRecordingStateChange) onRecordingStateChange(true);
                    } else {
                      console.error('WebSocket not in OPEN state when trying to start recording');
                    }
                  } else {
                    console.log('Microphone permission not granted, continuing without recording');
                    // The component will still work without microphone access
                  }
                } catch (micErr) {
                  console.log('Audio initialization failed, continuing without recording:', micErr);
                  // The component will still work without audio recording
                }
              }
            } catch (err) {
              console.error('Error auto-starting recording:', err);
              // Don't set error state to avoid confusing the user
              // The component will still work without recording
            }
          }
        };

        socket.onerror = () => {
          // Don't log the event object as it may be empty or circular
          console.error('WebSocket error occurred');

          // Only set error if we're not intentionally closing
          if (!isIntentionallyClosingRef.current) {
            setError('Connection error - please try again');
          }

          setIsConnected(false);
          if (onConnectionStateChange) onConnectionStateChange(false);

          // Try to close the socket to prevent further errors
          try {
            socket.close(1000, 'Error occurred');
          } catch (closeErr) {
            console.error('Error closing socket after error:', closeErr);
          }
        };

        socket.onmessage = async (event) => {
          try {
            const data = JSON.parse(event.data);

            // Define interruption indicators
            const INTERRUPTION_INDICATORS = [
              "conversation.item.input_audio_transcription.started", // User started speaking
              "response.interrupted",                                // Response was interrupted
              "conversation.item.input_audio_transcription.completed" // User finished speaking
            ];

            // Check for interruption indicators
            if (INTERRUPTION_INDICATORS.includes(data.type)) {

              // For explicit interruption, take immediate action
              if (data.type === 'response.interrupted') {
                // Clear any queued audio from the assistant
                audioUtils.clearAudioQueue();

                // Send a message to the server to acknowledge the interruption
                if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
                  try {
                    socketRef.current.send(JSON.stringify({
                      type: "interruption_acknowledged",
                      timestamp: Date.now()
                    }));
                  } catch (sendErr) {
                    console.error('Error sending interruption acknowledgment:', sendErr);
                  }
                }

                appendMessage("System", "Assistant interrupted");
              }
            }



            // Handle different message types
            switch (data.type) {
              case 'response.audio.delta':
                // Play the audio when we receive it
                if (data.delta && data.delta.length > 0) {
                  try {
                    // Process audio chunks
                    // Process audio immediately
                    await audioUtils.playAudio(data.delta);
                  } catch (err) {
                    console.error('Error playing audio:', err);
                  }
                }
                break;

              case 'response.audio_transcript.delta':
                appendMessage("Assistant", data.delta);
                break;

              case 'response.text.delta':
                appendMessage("Assistant", data.delta);
                break;

              case 'input_audio_buffer.speech_started':
                // Check if the assistant is currently speaking and this might be an interruption
                // We'll clear audio if we detect speech while assistant is speaking
                if (audioUtils.getAssistantSpeakingState()) {
                  audioUtils.clearAudioQueue();
                  appendMessage("System", "User started speaking");
                }
                break;

              case 'response.interrupted':
                // Already handled above, but keep the case for clarity
                break;

              case 'conversation.item.input_audio_transcription.completed':
                if (data.transcript) {
                  appendMessage("User", data.transcript);

                  // If this was an interruption (assistant was speaking), clear the audio queue
                  if (audioUtils.getAssistantSpeakingState()) {
                    audioUtils.clearAudioQueue();

                    // Send a message to the server to acknowledge the interruption
                    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
                      try {
                        socketRef.current.send(JSON.stringify({
                          type: "interruption_acknowledged",
                          timestamp: Date.now()
                        }));
                      } catch (sendErr) {
                        console.error('Error sending interruption acknowledgment:', sendErr);
                      }
                    }
                  }
                }
                break;

              // Handle completion message types with minimal logging
              case 'response.audio.done':
              case 'response.audio_transcript.done':
              case 'response.content_part.done':
              case 'response.output_item.done':
              case 'response.done':
              case 'input_audio_buffer.speech_stopped':
              case 'input_audio_buffer.committed':
                // These are completion messages, just log them minimally
                // No need for console.log here to reduce noise
                break;

              // Handle other message types that were previously "unhandled"
              case 'session.updated':
              case 'conversation.item.created':
              case 'response.created':
              case 'rate_limits.updated':
              case 'response.output_item.added':
              case 'response.content_part.added':
                // These are informational messages, just log them minimally
                // Reduce log frequency by not logging every message
                break;

              case 'error':
                // Handle case where message might be undefined
                const errorMessage = data.message || 'Unknown server error';
                console.error('Server error:', errorMessage);
                setError(`Server error: ${errorMessage}`);
                break;

              default:
                // Silently handle unknown message types
                break;
            }
          } catch (err) {
            console.error('Error parsing message:', err);
          }
        };

        socket.onclose = (event) => {
          // Only log errors for non-normal closures
          if (event.code !== 1000) {
            console.error(`WebSocket connection closed: code=${event.code}, reason="${event.reason}", wasClean=${event.wasClean}`);
          }

          setIsConnected(false);
          if (onConnectionStateChange) onConnectionStateChange(false);

          // Check if we're intentionally closing the connection
          if (isIntentionallyClosingRef.current) {
            return; // Don't attempt to reconnect
          }

          // Attempt to reconnect if not closed cleanly and we haven't exceeded max attempts
          if (!event.wasClean && reconnectAttemptsRef.current < WS_CONFIG.RECONNECT_ATTEMPTS) {
            reconnectAttemptsRef.current++;

            reconnectTimeoutRef.current = setTimeout(() => {
              connectWebSocket();
            }, WS_CONFIG.RECONNECT_DELAY);
          } else if (reconnectAttemptsRef.current >= WS_CONFIG.RECONNECT_ATTEMPTS) {
            console.error(`Max reconnection attempts reached for agent_role: ${agentRole}`);

            // For PPCA, we'll try one more time after a longer delay
            if (agentRole === AgentRoleEnum.PPCA && !isIntentionallyClosingRef.current) {
              reconnectTimeoutRef.current = setTimeout(() => {
                reconnectAttemptsRef.current = 0; // Reset the counter
                connectWebSocket();
              }, WS_CONFIG.FINAL_RECONNECT_DELAY);
            } else {
              setError(ERROR_MESSAGES.CONNECTION.MAX_ATTEMPTS);
            }
          }
        };

        socketRef.current = socket;
      } catch (err) {
        console.error('Error creating WebSocket:', err);
        setError('Failed to create connection');
        setIsConnected(false);
        if (onConnectionStateChange) onConnectionStateChange(false);
      }
    };

    // Initialize audio context only when needed
    useEffect(() => {
      // Just prepare the audio context but don't auto-start it
      audioUtils.initAudioContext(false).catch(err => {
        console.error('Audio context initialization error:', err);
        setError(ERROR_MESSAGES.AUDIO.INIT_FAILED);
      });
    }, []);



    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      startConnection: async () => {
        // Reset all global audio state to fix screen transition issues
        // Don't reset first message state if we're in reconnection mode
        audioUtils.resetGlobalAudioState(false);

        // Reset the unmounting flag in audioUtils to allow new operations
        audioUtils.setUnmountingFlag(false);

        return audioUtils.safeWebSocketOperation(async () => {
          // First, reinitialize the audio context
          try {
            await audioUtils.initAudioContext();
            console.log('Audio context initialized during connection start');
          } catch (audioErr) {
            console.error('Error initializing audio context:', audioErr);
          }

          // Connect to WebSocket
          await connectWebSocket();

          // Add a standardized delay to allow the connection to fully establish
          console.log(`Using standardized connection delay of ${AGENT_ROLES.CONNECTION_DELAY}ms for ${agentRole}`);

          await new Promise(resolve => setTimeout(resolve, AGENT_ROLES.CONNECTION_DELAY));

          // Set the WebSocket in the audio utils
          if (socketRef.current) {
            audioUtils.setWebSocket(socketRef.current);

            // Send a test message to verify the connection
            if (socketRef.current.readyState === WebSocket.OPEN) {
              try {
                socketRef.current.send(JSON.stringify({
                  type: "ping",
                  timestamp: Date.now()
                }));
                console.log('Sent ping to server during connection start');

                // No longer sending greeting message here to avoid duplicate messages
                // Only the initial WebSocket connection will send the greeting
              } catch (pingErr) {
                console.error('Error sending ping during connection start:', pingErr);
              }
            }

            // Return a resolved promise only if the connection is open
            if (socketRef.current.readyState === WebSocket.OPEN) {
              console.log('WebSocket connection is fully established');
              return Promise.resolve();
            } else {
              console.warn('WebSocket connection not fully established yet');
              // Still return resolved to avoid breaking the UI flow
              return Promise.resolve();
            }
          }

          return Promise.resolve();
        }, `AssistantChat (${agentRole})`, 'startConnection').catch(err => {
          // Only set error if not unmounting
          if (!audioUtils.isComponentUnmounting()) {
            console.error('Connection error:', err);
            // Use appropriate error message based on agent role
            const errorMessage = agentRole === AgentRoleEnum.PPCA ?
              ERROR_MESSAGES.CONNECTION.PPCA :
              ERROR_MESSAGES.CONNECTION.DEFAULT;

            setError(errorMessage);
          }
          return Promise.reject(err);
        });
      },
      startRecording: async () => {
        // Reset all global audio state to fix screen transition issues
        // Don't reset first message state if we're in reconnection mode
        audioUtils.resetGlobalAudioState(false);

        // Reset the unmounting flag in audioUtils to allow new operations
        audioUtils.setUnmountingFlag(false);

        return audioUtils.safeWebSocketOperation(async () => {
          // Check if WebSocket is connected
          if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
            console.log(`${componentName}: WebSocket not connected, attempting to connect...`);
            
            // Try to connect WebSocket first
            await connectWebSocket();
            
            // Wait for connection to be established
            let attempts = 0;
            while ((!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) && attempts < 5) {
              await new Promise(resolve => setTimeout(resolve, 200));
              attempts++;
            }
          }

          // Double-check that the connection is now open
          if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
            console.error('WebSocket still not connected after waiting');
            setError('Connection not ready. Please try again in a moment.');
            throw new Error('WebSocket not in OPEN state');
          }

          // Reinitialize audio context to ensure it's in a good state
          // Pass true to indicate this is from a user gesture
          try {
            await audioUtils.initAudioContext(true);
            console.log('Audio context reinitialized from user gesture');
          } catch (audioErr) {
            console.error('Error reinitializing audio context:', audioErr);
            // Try one more time after a minimal delay
            await new Promise(resolve => setTimeout(resolve, 100));
            await audioUtils.initAudioContext(true);
          }

          // Set the WebSocket in the audio utils
          audioUtils.setWebSocket(socketRef.current);

          // Send a test message to verify the connection
          try {
            socketRef.current.send(JSON.stringify({
              type: "ping",
              timestamp: Date.now()
            }));

            // No longer sending greeting message here to avoid duplicate messages
            // Only the initial WebSocket connection will send the greeting
          } catch (err) {
            console.error('Error sending ping:', err);
            setError('Connection error. Please try again.');
            throw err;
          }

          // Add a standardized delay before starting recording

          await new Promise(resolve => setTimeout(resolve, AGENT_ROLES.RECORDING_DELAY));

          // Start recording
          await audioUtils.startRecording();
          console.log('Recording started successfully');

          if (onRecordingStateChange) onRecordingStateChange(true);
        }, `AssistantChat (${agentRole})`, 'startRecording').catch(err => {
          // Only set error if not unmounting
          if (!audioUtils.isComponentUnmounting()) {
            console.error('Start recording error:', err);
            // Use appropriate error message based on agent role
            const errorMessage = agentRole === AgentRoleEnum.PPCA ?
              ERROR_MESSAGES.RECORDING.PPCA :
              ERROR_MESSAGES.RECORDING.DEFAULT;

            setError(errorMessage);

            // Try to clean up resources
            try {
              audioUtils.stopRecording();
              audioUtils.closeConnection();
            } catch (cleanupErr) {
              console.error('Error during cleanup after recording failure:', cleanupErr);
            }
          }

          throw err;
        });
      },
      stopRecording: () => {
        try {
          audioUtils.stopRecording();

          // Release microphone stream
          const micStream = audioUtils.getMicrophoneStream();
          if (micStream) {
            micStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
          }

          if (onRecordingStateChange) onRecordingStateChange(false);
        } catch (err) {
          console.error('Error stopping recording:', err);
        }
      },

      // Add a method to handle interruption
      handleInterruption: () => {
        try {
          // Clear any queued audio
          audioUtils.clearAudioQueue();

          // Send a message to the server to acknowledge the interruption
          if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
            try {
              socketRef.current.send(JSON.stringify({
                type: "interruption_acknowledged",
                timestamp: Date.now()
              }));
              console.log('Sent interruption acknowledgment to server');
            } catch (sendErr) {
              console.error('Error sending interruption acknowledgment:', sendErr);
            }
          }

          console.log('Handled interruption - cleared audio queue and notified server');
        } catch (err) {
          console.error('Error handling interruption:', err);
        }
      },
      // Fully end the conversation and close the WebSocket
      endConversation: () => {
        try {
          console.log('Ending conversation and preventing reconnection attempts');

          // Set the flag to indicate we're intentionally closing the connection
          isIntentionallyClosingRef.current = true;

          // Set the unmounting flag in audioUtils to prevent new operations
          try {
            audioUtils.setUnmountingFlag(true);
          } catch (e) {
            console.log('Error setting unmounting flag in audioUtils:', e);
          }

          // First, immediately clear any remaining audio to stop playback
          try {
            audioUtils.clearAudioQueue();
            console.log('Audio queue cleared immediately to stop voice playback');
          } catch (e) {
            console.log('Error clearing audio queue:', e);
          }

          // Clear any pending reconnect timeouts
          if (reconnectTimeoutRef.current) {
            try {
              clearTimeout(reconnectTimeoutRef.current);
              reconnectTimeoutRef.current = null;
            } catch (e) {
              console.log('Error clearing reconnect timeout:', e);
            }
          }

          // Stop recording if active
          try {
            audioUtils.stopRecording();
            if (onRecordingStateChange) onRecordingStateChange(false);
          } catch (e) {
            console.log('Error stopping recording:', e);
          }

          // Release microphone stream
          try {
            const micStream = audioUtils.getMicrophoneStream();
            if (micStream) {
              micStream.getTracks().forEach((track: MediaStreamTrack) => {
                try {
                  track.stop();
                } catch (e) {
                  console.log('Error stopping track:', e);
                }
              });
            }
          } catch (e) {
            console.log('Error releasing microphone stream:', e);
          }

          // Send end conversation message
          if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
            try {
              socketRef.current.send(JSON.stringify({ type: 'end_conversation' }));
            } catch (err) {
              console.log('Error sending end_conversation:', err);
            }
          }

          // Close the socket
          if (socketRef.current) {
            try {
              socketRef.current.close(1000, 'User ended conversation');
            } catch (err) {
              console.log('Error closing WebSocket:', err);
            } finally {
              socketRef.current = null;
            }
          }

          // Close audio context and release resources
          try {
            audioUtils.closeConnection();
          } catch (e) {
            console.log('Error closing audio connection:', e);
          }

          // Clear global socket reference
          try {
            if (typeof window !== 'undefined' && (window as any).assistantSocket) {
              (window as any).assistantSocket = null;
            }
          } catch (e) {
            console.log('Error clearing global socket reference:', e);
          }

          // Reset the paused state
          setIsPaused(false);

          // Notify parent component that conversation is complete
          try {
            if (onConversationComplete) onConversationComplete();
          } catch (e) {
            console.log('Error in onConversationComplete callback:', e);
          }
        } catch (err) {
          console.log('Error in endConversation:', err);
        } finally {
          // Reset the flag after a short delay to allow for future connections if needed
          setTimeout(() => {
            try {
              isIntentionallyClosingRef.current = false;
              // Reset the unmounting flag in audioUtils
              audioUtils.setUnmountingFlag(false);
            } catch (e) {
              console.log('Error in endConversation finally block:', e);
            }
          }, 100); // Reduced from 500ms to 100ms for faster cleanup
        }
      },

      // Pause the conversation without fully closing the WebSocket
      pauseConversation: () => {
        try {
          console.log('Pausing conversation (not fully closing)');

          // First, immediately clear any remaining audio to stop playback
          // This ensures no more audio will be played after pausing
          audioUtils.clearAudioQueue();
          console.log('Audio queue cleared immediately to stop voice playback');

          // Stop recording if active
          audioUtils.stopRecording();
          if (onRecordingStateChange) onRecordingStateChange(false);

          // Release microphone stream but keep WebSocket alive
          const micStream = audioUtils.getMicrophoneStream();
          if (micStream) {
            micStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
          }

          // Send pause message to server if needed
          if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
            try {
              // Send a special message to indicate immediate audio stop
              socketRef.current.send(JSON.stringify({
                type: 'pause_conversation',
                stop_audio: true,
                timestamp: Date.now()
              }));
            } catch (err) {
              console.error('Error sending pause_conversation:', err);
            }
          }

          // Set the paused state to true
          setIsPaused(true);

          // Set the WebSocket paused state in audioUtils
          audioUtils.setWebSocketPausedState(true);

          // Double-check that audio context is suspended to prevent any audio playback
          if (audioUtils.getAudioContext() && audioUtils.getAudioContext()?.state === 'running') {
            try {
              audioUtils.getAudioContext()?.suspend().catch(err => {
                console.error('Error suspending audio context during pause:', err);
              });
            } catch (err) {
              console.error('Error suspending audio context during pause:', err);
            }
          }

          console.log('Conversation paused, WebSocket kept alive, audio completely stopped');
        } catch (err) {
          console.error('Error in pauseConversation:', err);
        }
      },

      // Resume a paused conversation
      resumeConversation: async () => {
        try {
          console.log('Resuming paused conversation');

          // Reset all global audio state to ensure clean resume, but preserve first message state
          audioUtils.resetGlobalAudioState(false); // Don't reset first message state for resume

          // If the WebSocket is closed or closing, reconnect it
          if (!socketRef.current || socketRef.current.readyState === WebSocket.CLOSED || socketRef.current.readyState === WebSocket.CLOSING) {
            console.log('WebSocket is closed, reconnecting before resuming');
            await connectWebSocket();

            // Add a small delay to ensure the connection is established
            await new Promise(resolve => setTimeout(resolve, 300));
          }

          // Send resume message to server if needed
          if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
            try {
              socketRef.current.send(JSON.stringify({ type: 'resume_conversation' }));

              // No longer sending greeting message here to avoid duplicate messages
              // Only the initial WebSocket connection will send the greeting
            } catch (err) {
              console.error('Error sending resume_conversation message:', err);
            }
          }

          // Set the paused state to false
          setIsPaused(false);

          // Set the WebSocket paused state in audioUtils
          audioUtils.setWebSocketPausedState(false);

          // Set the WebSocket in the audio utils to ensure it's properly connected
          if (socketRef.current) {
            audioUtils.setWebSocket(socketRef.current);
          }


        } catch (err) {
          console.error('Error in resumeConversation:', err);
          throw err;
        }
      },
      // Expose the socket reference to parent components
      get assistantSocket() {
        return socketRef.current;
      },
      // Expose the paused state to parent components
      get isPaused() {
        return isPaused;
      },
    }));

    return <div className="hidden">Assistant Chat Component</div>;
  }
);

AssistantChat.displayName = 'AssistantChat';

export default AssistantChat;
