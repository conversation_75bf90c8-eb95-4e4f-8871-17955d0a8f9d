// Voice Conversation Logging System
// Tracks and stores meta logs during assistant-user conversations to diagnose voice/audio-related issues

export interface VoiceLogEntry {
  // Who spoke (user or assistant)
  speaker: 'user' | 'assistant';
  
  // Timestamp of message
  timestamp: number;
  
  // Voice frequency / amplitude or any audio meta-info available
  audioMetadata?: {
    frequency?: number;
    amplitude?: number;
    duration?: number;
    sampleRate?: number;
    channels?: number;
  };
  
  // Optional details like errors, latency, etc.
  details?: {
    messageType?: string;
    latency?: number;
    error?: string;
    transcription?: string;
    audioDataSize?: number;
    processingTime?: number;
  };
  
  // Session information
  sessionId?: string;
  userId?: string;
  agentRole?: string;
}

// Global queue to store voice logs
let voiceLogQueue: VoiceLogEntry[] = [];

// Session ID for current conversation
let currentSessionId: string | null = null;

// Maximum number of logs to keep in memory (prevent memory leaks)
const MAX_LOG_QUEUE_SIZE = 1000;

/**
 * Initialize a new voice logging session
 * @param sessionId - Unique identifier for the conversation session
 * @param userId - User ID for the current user
 * @param agentRole - Role of the agent (PPCA, OBA, etc.)
 */
export function initVoiceLoggingSession(sessionId: string, userId?: string, agentRole?: string): void {
  currentSessionId = sessionId;
  voiceLogQueue = []; // Clear previous logs
  
  console.log(`Voice logging session initialized: ${sessionId}`);
  
  // Log session start
  logVoiceMessage({
    speaker: 'assistant',
    timestamp: Date.now(),
    details: {
      messageType: 'session_start',
    },
    sessionId,
    userId,
    agentRole
  });
}

/**
 * Global logging function to append logs to a global queue (not send immediately)
 * @param metadata - Voice message metadata to log
 */
export function logVoiceMessage(metadata: Omit<VoiceLogEntry, 'sessionId' | 'userId' | 'agentRole'> & {
  sessionId?: string;
  userId?: string;
  agentRole?: string;
}): void {
  try {
    // Create the log entry with session info
    const logEntry: VoiceLogEntry = {
      ...metadata,
      sessionId: metadata.sessionId || currentSessionId || undefined,
      timestamp: metadata.timestamp || Date.now(),
    };
    
    // Add to queue
    voiceLogQueue.push(logEntry);
    
    // Prevent memory leaks by limiting queue size
    if (voiceLogQueue.length > MAX_LOG_QUEUE_SIZE) {
      voiceLogQueue = voiceLogQueue.slice(-MAX_LOG_QUEUE_SIZE);
      console.warn('Voice log queue exceeded maximum size, oldest logs removed');
    }
    
    // Debug logging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('Voice log added:', {
        speaker: logEntry.speaker,
        messageType: logEntry.details?.messageType,
        timestamp: new Date(logEntry.timestamp).toISOString(),
        queueSize: voiceLogQueue.length
      });
    }
  } catch (error) {
    console.error('Error adding voice log:', error);
  }
}

/**
 * Get current voice log queue (for debugging)
 * @returns Array of voice log entries
 */
export function getVoiceLogQueue(): VoiceLogEntry[] {
  return [...voiceLogQueue]; // Return a copy to prevent external modification
}

/**
 * Clear the voice log queue
 */
export function clearVoiceLogQueue(): void {
  voiceLogQueue = [];
  console.log('Voice log queue cleared');
}

/**
 * Send voice logs in batch to the backend
 * @param reason - Reason for sending logs (e.g., 'websocket_cleanup', 'conversation_exit')
 * @returns Promise<boolean> - True if logs were sent successfully
 */
export async function sendVoiceLogsBatch(reason: string): Promise<boolean> {
  if (voiceLogQueue.length === 0) {
    console.log('No voice logs to send');
    return true;
  }
  
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('No auth token found, cannot send voice logs');
      return false;
    }
    
    const logsToSend = [...voiceLogQueue]; // Copy the queue
    const batchData = {
      logs: logsToSend,
      reason,
      timestamp: Date.now(),
      sessionId: currentSessionId,
      totalLogs: logsToSend.length
    };
    
    console.log(`Sending ${logsToSend.length} voice logs (reason: ${reason})`);
    
    // Send to our Next.js API route
    const response = await fetch('/api/frontend-logs/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(batchData),
      // Use keepalive for cleanup scenarios
      ...(reason.includes('cleanup') || reason.includes('exit') ? { keepalive: true } : {})
    });
    
    if (response.ok) {
      console.log('Voice logs sent successfully');
      clearVoiceLogQueue(); // Clear queue after successful send
      return true;
    } else {
      console.error('Failed to send voice logs:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('Error sending voice logs batch:', error);
    return false;
  }
}

/**
 * Log session end and send final batch
 * @param reason - Reason for ending session
 */
export async function endVoiceLoggingSession(reason: string = 'session_end'): Promise<void> {
  if (currentSessionId) {
    // Log session end
    logVoiceMessage({
      speaker: 'assistant',
      timestamp: Date.now(),
      details: {
        messageType: 'session_end',
      }
    });
    
    // Send final batch
    await sendVoiceLogsBatch(reason);
    
    // Clear session
    currentSessionId = null;
    console.log('Voice logging session ended');
  }
}

/**
 * Emergency cleanup for voice logging (e.g., during logout)
 */
export function emergencyVoiceLoggingCleanup(): void {
  console.log('Emergency voice logging cleanup');
  
  // Try to send logs with keepalive if possible
  if (voiceLogQueue.length > 0) {
    sendVoiceLogsBatch('emergency_cleanup').catch(err => {
      console.error('Error during emergency voice log cleanup:', err);
    });
  }
  
  // Clear everything
  voiceLogQueue = [];
  currentSessionId = null;
}
