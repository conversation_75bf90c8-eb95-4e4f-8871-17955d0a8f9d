// Voice Conversation Logging System
// Tracks and stores meta logs during assistant-user conversations to diagnose voice/audio-related issues

export interface VoiceLogEntry {
  // Who spoke (user or assistant)
  speaker: 'user' | 'assistant';
  
  // Timestamp of message
  timestamp: number;
  
  // Voice frequency / amplitude or any audio meta-info available
  audioMetadata?: {
    frequencyHz?: number;
    amplitudeDb?: number;
    duration?: number;
    sampleRate?: number;
    channels?: number;
    audioDataSize?: number;
    silenceDetected?: boolean;
    peakFrequency?: number;
    averageAmplitude?: number;
    rmsAmplitude?: number;
    maxAmplitude?: number;
    peakRMS?: number;
    playbackStartTime?: number;
    playbackEndTime?: number;
    playbackLatency?: number;
    actualPlaybackDuration?: number;
  };
  
  // Optional details like errors, latency, etc.
  details?: {
    messageType?: string;
    latency?: number;
    error?: string;
    audioDataSize?: number;
    processingTime?: number;
    aggregatedChunks?: number;
    // iPhone debugging fields
    iphoneDebugging?: boolean;
    hasFrequencyData?: boolean;
    hasAmplitudeData?: boolean;
    // Note: transcription content excluded for privacy
  };
  
  // Session information
  sessionId?: string;
  userId?: string;
  agentRole?: string;

  // Device environment (captured once per session)
  deviceEnvironment?: {
    device: string;
    browser: string;
    osVersion: string;
    outputDevice: string;
    networkType: string;
    userAgent?: string;
    screenResolution?: string;
    audioCapabilities?: {
      audioContext: boolean;
      webAudio: boolean;
      mediaDevices: boolean;
      audioWorklet: boolean;
    };
    permissions?: {
      microphone: string;
      notifications: string;
    };
  };
}

// Global queue to store voice logs
let voiceLogQueue: VoiceLogEntry[] = [];

// Session ID for current conversation
let currentSessionId: string | null = null;

// Maximum number of logs to keep in memory (prevent memory leaks)
const MAX_LOG_QUEUE_SIZE = 1000;

// Track sent batches to prevent duplicates
let sentBatchHashes: Set<string> = new Set();
let batchSendInProgress = false;
let lastBatchSentTime = 0;

// Throttling and aggregation state
let lastAudioLogTime = 0;
let lastTranscriptLogTime = 0;
let audioChunkCount = 0;
let transcriptChunkCount = 0;
let totalAudioDataSize = 0;

const AUDIO_LOG_THROTTLE_MS = 2000; // Log audio every 2 seconds max
const TRANSCRIPT_LOG_THROTTLE_MS = 3000; // Log transcript every 3 seconds max
const MIN_CHUNKS_FOR_LOG = 3; // Minimum chunks before logging
const MAX_CHUNKS_FOR_LOG = 10; // Maximum chunks before forcing a log

/**
 * Initialize a new voice logging session
 * @param sessionId - Unique identifier for the conversation session
 * @param userId - User ID for the current user
 * @param agentRole - Role of the agent (PPCA, OBA, etc.)
 */
export function initVoiceLoggingSession(sessionId: string, userId?: string, agentRole?: string): void {
  currentSessionId = sessionId;
  voiceLogQueue = []; // Clear previous logs
  resetDeduplicationState(); // Reset duplicate prevention

  console.log(`Voice logging session initialized: ${sessionId}`);

  // Collect device environment asynchronously
  import('./deviceDetection').then(({ collectDeviceEnvironment }) => {
    collectDeviceEnvironment().then(deviceEnv => {
      // Log session start with device environment
      logVoiceMessage({
        speaker: 'assistant',
        timestamp: Date.now(),
        details: {
          messageType: 'session_start',
        },
        sessionId,
        userId,
        agentRole,
        deviceEnvironment: deviceEnv
      });

      console.log('Device environment captured:', {
        device: deviceEnv.device,
        browser: deviceEnv.browser,
        osVersion: deviceEnv.osVersion,
        outputDevice: deviceEnv.outputDevice,
        networkType: deviceEnv.networkType
      });
    }).catch(err => {
      console.log('Error collecting device environment:', err);

      // Log session start without device environment
      logVoiceMessage({
        speaker: 'assistant',
        timestamp: Date.now(),
        details: {
          messageType: 'session_start',
        },
        sessionId,
        userId,
        agentRole
      });
    });
  }).catch(err => {
    console.log('Error importing device detection:', err);

    // Log session start without device environment
    logVoiceMessage({
      speaker: 'assistant',
      timestamp: Date.now(),
      details: {
        messageType: 'session_start',
      },
      sessionId,
      userId,
      agentRole
    });
  });
}



/**
 * Check if transcript logging should be throttled and get aggregation data
 */
function getTranscriptLogAggregation(): { shouldLog: boolean; aggregatedChunks: number } {
  const now = Date.now();
  transcriptChunkCount++;

  // Force log if we've hit the maximum chunks
  if (transcriptChunkCount >= MAX_CHUNKS_FOR_LOG) {
    lastTranscriptLogTime = now;
    const result = { shouldLog: true, aggregatedChunks: transcriptChunkCount };
    transcriptChunkCount = 0;
    return result;
  }

  // Always log if it's been more than the throttle time
  if (now - lastTranscriptLogTime > TRANSCRIPT_LOG_THROTTLE_MS) {
    lastTranscriptLogTime = now;
    const result = { shouldLog: true, aggregatedChunks: transcriptChunkCount };
    transcriptChunkCount = 0;
    return result;
  }

  // Or if we've accumulated enough chunks
  if (transcriptChunkCount >= MIN_CHUNKS_FOR_LOG) {
    lastTranscriptLogTime = now;
    const result = { shouldLog: true, aggregatedChunks: transcriptChunkCount };
    transcriptChunkCount = 0;
    return result;
  }

  return { shouldLog: false, aggregatedChunks: transcriptChunkCount };
}

/**
 * Enhanced logging function for audio chunks - ALWAYS logs for iPhone debugging
 * Captures complete frequency/amplitude data for every audio event
 */
export function logVoiceAudioChunk(metadata: Omit<VoiceLogEntry, 'sessionId' | 'userId' | 'agentRole'> & {
  sessionId?: string;
  userId?: string;
  agentRole?: string;
}): void {
  // For iPhone debugging, we need EVERY audio chunk with complete metadata
  // Only throttle if we have very high frequency (>50 chunks in 1 second)
  const now = Date.now();
  audioChunkCount++;

  // Force log if we have audio metadata (frequency/amplitude data)
  const hasAudioDiagnostics = metadata.audioMetadata && (
    metadata.audioMetadata.frequencyHz !== undefined ||
    metadata.audioMetadata.amplitudeDb !== undefined ||
    metadata.audioMetadata.silenceDetected !== undefined
  );

  // Always log if we have audio diagnostics OR if it's been >500ms since last log
  const shouldLog = hasAudioDiagnostics ||
                   (now - lastAudioLogTime > 500) ||
                   (audioChunkCount >= 5);

  if (shouldLog) {
    lastAudioLogTime = now;
    const chunksToReport = audioChunkCount;
    audioChunkCount = 0;
    totalAudioDataSize += metadata.audioMetadata?.audioDataSize || 0;

    logVoiceMessage({
      ...metadata,
      details: {
        ...metadata.details,
        aggregatedChunks: chunksToReport,
        // Add iPhone debugging flags
        iphoneDebugging: true,
        hasFrequencyData: !!metadata.audioMetadata?.frequencyHz,
        hasAmplitudeData: !!metadata.audioMetadata?.amplitudeDb
      },
      audioMetadata: {
        ...metadata.audioMetadata,
        // Ensure we always have these fields for iPhone debugging
        audioDataSize: metadata.audioMetadata?.audioDataSize || 0,
        silenceDetected: metadata.audioMetadata?.silenceDetected ?? true,
        frequencyHz: metadata.audioMetadata?.frequencyHz || 0,
        amplitudeDb: metadata.audioMetadata?.amplitudeDb ?? -Infinity
      }
    });
  }
}

/**
 * Optimized logging function for transcript chunks with throttling
 */
export function logVoiceTranscriptChunk(metadata: Omit<VoiceLogEntry, 'sessionId' | 'userId' | 'agentRole'> & {
  sessionId?: string;
  userId?: string;
  agentRole?: string;
}): void {
  // Get aggregation data and check if we should log
  const aggregation = getTranscriptLogAggregation();

  if (!aggregation.shouldLog) {
    return;
  }

  logVoiceMessage({
    ...metadata,
    details: {
      ...metadata.details,
      aggregatedChunks: aggregation.aggregatedChunks
    }
  });
}

/**
 * Global logging function to append logs to a global queue (not send immediately)
 * @param metadata - Voice message metadata to log
 */
export function logVoiceMessage(metadata: Omit<VoiceLogEntry, 'sessionId' | 'userId' | 'agentRole'> & {
  sessionId?: string;
  userId?: string;
  agentRole?: string;
}): void {
  try {
    // Create the log entry with session info
    const logEntry: VoiceLogEntry = {
      ...metadata,
      sessionId: metadata.sessionId || currentSessionId || undefined,
      timestamp: metadata.timestamp || Date.now(),
    };

    // Add to queue
    voiceLogQueue.push(logEntry);

    // Prevent memory leaks by limiting queue size
    if (voiceLogQueue.length > MAX_LOG_QUEUE_SIZE) {
      voiceLogQueue = voiceLogQueue.slice(-MAX_LOG_QUEUE_SIZE);
      console.warn('Voice log queue exceeded maximum size, oldest logs removed');
    }

    // Debug logging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('Voice log added:', {
        speaker: logEntry.speaker,
        messageType: logEntry.details?.messageType,
        timestamp: new Date(logEntry.timestamp).toISOString(),
        queueSize: voiceLogQueue.length
      });
    }
  } catch (error) {
    console.error('Error adding voice log:', error);
  }
}

/**
 * Get current voice log queue (for debugging)
 * @returns Array of voice log entries
 */
export function getVoiceLogQueue(): VoiceLogEntry[] {
  return [...voiceLogQueue]; // Return a copy to prevent external modification
}

/**
 * Clear the voice log queue
 */
export function clearVoiceLogQueue(): void {
  voiceLogQueue = [];
  console.log('Voice log queue cleared');
}

/**
 * Reset deduplication state (called when starting new session)
 */
function resetDeduplicationState(): void {
  sentBatchHashes.clear();
  batchSendInProgress = false;
  lastBatchSentTime = 0;
}

/**
 * Generate a hash for a batch of logs to detect duplicates
 */
function generateBatchHash(logs: VoiceLogEntry[], reason: string, sessionId: string | null): string {
  const hashData = {
    sessionId,
    reason,
    logCount: logs.length,
    firstLogTimestamp: logs[0]?.timestamp || 0,
    lastLogTimestamp: logs[logs.length - 1]?.timestamp || 0,
    // Add more specific identifiers to prevent false duplicates
    messageTypes: logs.map(log => log.details?.messageType).slice(0, 5), // First 5 message types
    speakers: logs.map(log => log.speaker).slice(0, 5), // First 5 speakers
    totalAudioData: logs.reduce((sum, log) => sum + (log.audioMetadata?.audioDataSize || 0), 0)
  };
  return btoa(JSON.stringify(hashData));
}

/**
 * Clean up old batch hashes to prevent memory leaks
 */
function cleanupOldBatchHashes(): void {
  // Keep only the last 50 hashes to prevent memory leaks
  if (sentBatchHashes.size > 50) {
    const hashArray = Array.from(sentBatchHashes);
    sentBatchHashes.clear();
    // Keep the most recent 25 hashes
    hashArray.slice(-25).forEach(hash => sentBatchHashes.add(hash));
  }
}

/**
 * Send voice logs in batch to the backend
 * @param reason - Reason for sending logs (e.g., 'websocket_cleanup', 'conversation_exit')
 * @returns Promise<boolean> - True if logs were sent successfully
 */
export async function sendVoiceLogsBatch(reason: string): Promise<boolean> {
  if (voiceLogQueue.length === 0) {
    console.log('No voice logs to send');
    return true;
  }

  // Prevent concurrent batch sends
  if (batchSendInProgress) {
    console.log('Batch send already in progress, skipping duplicate request');
    return false;
  }

  // Prevent rapid duplicate sends (within 1 second)
  const now = Date.now();
  if (now - lastBatchSentTime < 1000) {
    console.log('Batch sent too recently, skipping duplicate request');
    return false;
  }

  // Additional validation: check for empty or invalid logs
  const validLogs = voiceLogQueue.filter(log =>
    log.speaker &&
    log.timestamp &&
    log.timestamp > 0 &&
    log.details?.messageType
  );

  if (validLogs.length === 0) {
    console.log('No valid logs to send after filtering');
    return true;
  }

  if (validLogs.length !== voiceLogQueue.length) {
    console.warn(`Filtered out ${voiceLogQueue.length - validLogs.length} invalid logs`);
  }

  try {
    batchSendInProgress = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('No auth token found, cannot send voice logs');
      return false;
    }

    const logsToSend = [...validLogs]; // Use validated logs
    const batchHash = generateBatchHash(logsToSend, reason, currentSessionId);

    // Check if we've already sent this exact batch
    if (sentBatchHashes.has(batchHash)) {
      console.log('Duplicate batch detected, skipping send');
      return true; // Return true since the logs were already sent
    }

    const batchData = {
      logs: logsToSend,
      reason,
      timestamp: now,
      sessionId: currentSessionId,
      totalLogs: logsToSend.length,
      batchHash
    };

    console.log(`Sending ${logsToSend.length} voice logs (reason: ${reason}, hash: ${batchHash.substring(0, 8)}...)`);

    // Send to our Next.js API route
    const response = await fetch('/api/frontend-logs/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(batchData),
      // Use keepalive for cleanup scenarios
      ...(reason.includes('cleanup') || reason.includes('exit') ? { keepalive: true } : {})
    });

    if (response.ok) {
      console.log('Voice logs sent successfully');
      sentBatchHashes.add(batchHash);
      lastBatchSentTime = now;

      // Clean up old hashes periodically
      cleanupOldBatchHashes();

      clearVoiceLogQueue(); // Clear queue after successful send
      return true;
    } else {
      console.error('Failed to send voice logs:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('Error sending voice logs batch:', error);
    return false;
  } finally {
    batchSendInProgress = false;
  }
}

/**
 * Log session end and send final batch
 * @param reason - Reason for ending session
 */
export async function endVoiceLoggingSession(reason: string = 'session_end'): Promise<void> {
  if (currentSessionId) {
    // Log session end
    logVoiceMessage({
      speaker: 'assistant',
      timestamp: Date.now(),
      details: {
        messageType: 'session_end',
      }
    });
    
    // Send final batch
    await sendVoiceLogsBatch(reason);
    
    // Clear session
    currentSessionId = null;
    console.log('Voice logging session ended');
  }
}

/**
 * Emergency cleanup for voice logging (e.g., during logout)
 */
export function emergencyVoiceLoggingCleanup(): void {
  console.log('Emergency voice logging cleanup');
  
  // Try to send logs with keepalive if possible
  if (voiceLogQueue.length > 0) {
    sendVoiceLogsBatch('emergency_cleanup').catch(err => {
      console.error('Error during emergency voice log cleanup:', err);
    });
  }
  
  // Clear everything
  voiceLogQueue = [];
  currentSessionId = null;
}
