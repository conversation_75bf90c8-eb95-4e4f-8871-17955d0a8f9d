// Device and Environment Detection for Voice Logging
// Captures device type, browser, OS version, output device, and network info

export interface DeviceEnvironment {
  device: string;
  browser: string;
  osVersion: string;
  outputDevice: string;
  networkType: string;
  userAgent: string;
  screenResolution: string;
  audioCapabilities: {
    audioContext: boolean;
    webAudio: boolean;
    mediaDevices: boolean;
    audioWorklet: boolean;
  };
  permissions: {
    microphone: string;
    notifications: string;
  };
}

/**
 * Detect device type from user agent
 */
function detectDevice(): string {
  if (typeof navigator === 'undefined') return 'Unknown';
  
  const userAgent = navigator.userAgent;
  
  // iPhone detection
  if (/iPhone/.test(userAgent)) {
    const match = userAgent.match(/iPhone OS (\d+)_(\d+)/);
    if (match) {
      return `iPhone (iOS ${match[1]}.${match[2]})`;
    }
    return 'iPhone';
  }
  
  // iPad detection
  if (/iPad/.test(userAgent) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)) {
    return 'iPad';
  }
  
  // Android detection
  if (/Android/.test(userAgent)) {
    const match = userAgent.match(/Android (\d+\.?\d*)/);
    if (match) {
      return `Android ${match[1]}`;
    }
    return 'Android';
  }
  
  // Mac detection
  if (/Mac/.test(userAgent)) {
    return 'Mac';
  }
  
  // Windows detection
  if (/Windows/.test(userAgent)) {
    if (/Windows NT 10/.test(userAgent)) return 'Windows 10/11';
    if (/Windows NT 6\.3/.test(userAgent)) return 'Windows 8.1';
    if (/Windows NT 6\.2/.test(userAgent)) return 'Windows 8';
    if (/Windows NT 6\.1/.test(userAgent)) return 'Windows 7';
    return 'Windows';
  }
  
  // Linux detection
  if (/Linux/.test(userAgent)) {
    return 'Linux';
  }
  
  return 'Unknown';
}

/**
 * Detect browser and version
 */
function detectBrowser(): string {
  if (typeof navigator === 'undefined') return 'Unknown';
  
  const userAgent = navigator.userAgent;
  
  // Safari detection (must be before Chrome check)
  if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
    const match = userAgent.match(/Version\/(\d+\.?\d*)/);
    if (match) {
      return `Safari ${match[1]}`;
    }
    return 'Safari';
  }
  
  // Chrome detection
  if (/Chrome/.test(userAgent)) {
    const match = userAgent.match(/Chrome\/(\d+\.?\d*)/);
    if (match) {
      return `Chrome ${match[1]}`;
    }
    return 'Chrome';
  }
  
  // Firefox detection
  if (/Firefox/.test(userAgent)) {
    const match = userAgent.match(/Firefox\/(\d+\.?\d*)/);
    if (match) {
      return `Firefox ${match[1]}`;
    }
    return 'Firefox';
  }
  
  // Edge detection
  if (/Edg/.test(userAgent)) {
    const match = userAgent.match(/Edg\/(\d+\.?\d*)/);
    if (match) {
      return `Edge ${match[1]}`;
    }
    return 'Edge';
  }
  
  return 'Unknown';
}

/**
 * Detect OS version
 */
function detectOSVersion(): string {
  if (typeof navigator === 'undefined') return 'Unknown';
  
  const userAgent = navigator.userAgent;
  
  // iOS version
  const iosMatch = userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);
  if (iosMatch) {
    return `iOS ${iosMatch[1]}.${iosMatch[2]}${iosMatch[3] ? '.' + iosMatch[3] : ''}`;
  }
  
  // Android version
  const androidMatch = userAgent.match(/Android (\d+\.?\d*\.?\d*)/);
  if (androidMatch) {
    return `Android ${androidMatch[1]}`;
  }
  
  // macOS version
  const macMatch = userAgent.match(/Mac OS X (\d+)_(\d+)_?(\d+)?/);
  if (macMatch) {
    return `macOS ${macMatch[1]}.${macMatch[2]}${macMatch[3] ? '.' + macMatch[3] : ''}`;
  }
  
  // Windows version
  if (/Windows NT 10/.test(userAgent)) return 'Windows 10/11';
  if (/Windows NT 6\.3/.test(userAgent)) return 'Windows 8.1';
  if (/Windows NT 6\.2/.test(userAgent)) return 'Windows 8';
  if (/Windows NT 6\.1/.test(userAgent)) return 'Windows 7';
  
  return 'Unknown';
}

/**
 * Detect audio output device (limited by browser security)
 */
async function detectOutputDevice(): Promise<string> {
  try {
    if (typeof navigator !== 'undefined' && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioOutputs = devices.filter(device => device.kind === 'audiooutput');
      
      if (audioOutputs.length > 0) {
        // Most browsers don't provide device labels without permission
        const defaultDevice = audioOutputs.find(device => device.deviceId === 'default');
        if (defaultDevice && defaultDevice.label) {
          return defaultDevice.label;
        }
        return `${audioOutputs.length} audio output(s) available`;
      }
    }
  } catch (error) {
    console.log('Error detecting output device:', error);
  }
  
  return 'Unknown';
}

/**
 * Detect network type
 */
function detectNetworkType(): string {
  try {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection) {
        return connection.effectiveType || connection.type || 'Unknown';
      }
    }
  } catch (error) {
    console.log('Error detecting network type:', error);
  }
  
  return 'Unknown';
}

/**
 * Check audio capabilities
 */
function checkAudioCapabilities(): DeviceEnvironment['audioCapabilities'] {
  return {
    audioContext: typeof AudioContext !== 'undefined' || typeof (window as any).webkitAudioContext !== 'undefined',
    webAudio: typeof AudioContext !== 'undefined',
    mediaDevices: typeof navigator !== 'undefined' && !!navigator.mediaDevices,
    audioWorklet: typeof AudioWorkletNode !== 'undefined'
  };
}

/**
 * Check permissions status
 */
async function checkPermissions(): Promise<DeviceEnvironment['permissions']> {
  const permissions: DeviceEnvironment['permissions'] = {
    microphone: 'unknown',
    notifications: 'unknown'
  };
  
  try {
    if (typeof navigator !== 'undefined' && navigator.permissions) {
      // Check microphone permission
      try {
        const micPermission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        permissions.microphone = micPermission.state;
      } catch (err) {
        console.log('Error checking microphone permission:', err);
      }
      
      // Check notifications permission
      try {
        const notificationPermission = await navigator.permissions.query({ name: 'notifications' as PermissionName });
        permissions.notifications = notificationPermission.state;
      } catch (err) {
        console.log('Error checking notification permission:', err);
      }
    }
  } catch (error) {
    console.log('Error checking permissions:', error);
  }
  
  return permissions;
}

/**
 * Get screen resolution
 */
function getScreenResolution(): string {
  if (typeof window !== 'undefined' && window.screen) {
    return `${window.screen.width}x${window.screen.height}`;
  }
  return 'Unknown';
}

/**
 * Collect comprehensive device and environment information
 */
export async function collectDeviceEnvironment(): Promise<DeviceEnvironment> {
  const [outputDevice, permissions] = await Promise.all([
    detectOutputDevice(),
    checkPermissions()
  ]);
  
  return {
    device: detectDevice(),
    browser: detectBrowser(),
    osVersion: detectOSVersion(),
    outputDevice,
    networkType: detectNetworkType(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
    screenResolution: getScreenResolution(),
    audioCapabilities: checkAudioCapabilities(),
    permissions
  };
}
