// Audio Analysis Utilities for Voice Logging
// Extracts frequency, amplitude, and silence detection from audio data

export interface AudioAnalysisResult {
  frequencyHz: number;
  amplitudeDb: number;
  audioDataSize: number;
  sampleRate: number;
  channels: number;
  silenceDetected: boolean;
  peakFrequency?: number;
  averageAmplitude?: number;
  rmsAmplitude?: number;
}

/**
 * Analyze audio data to extract frequency and amplitude information
 * @param audioData - Base64 encoded audio data or raw audio buffer
 * @param sampleRate - Sample rate of the audio
 * @param channels - Number of audio channels
 * @returns AudioAnalysisResult with frequency and amplitude data
 */
export function analyzeAudioData(
  audioData: string | ArrayBuffer | Float32Array,
  sampleRate: number = 24000,
  channels: number = 1
): AudioAnalysisResult {
  try {
    let audioBuffer: Float32Array;
    let dataSize: number;

    // Convert different input types to Float32Array
    if (typeof audioData === 'string') {
      // Base64 encoded data
      const binaryString = atob(audioData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      // Convert bytes to Int16Array then to Float32Array
      const int16Array = new Int16Array(bytes.buffer);
      audioBuffer = new Float32Array(int16Array.length);
      for (let i = 0; i < int16Array.length; i++) {
        audioBuffer[i] = int16Array[i] / 32768.0; // Normalize to [-1, 1]
      }
      dataSize = binaryString.length;
    } else if (audioData instanceof ArrayBuffer) {
      const int16Array = new Int16Array(audioData);
      audioBuffer = new Float32Array(int16Array.length);
      for (let i = 0; i < int16Array.length; i++) {
        audioBuffer[i] = int16Array[i] / 32768.0;
      }
      dataSize = audioData.byteLength;
    } else {
      // Already Float32Array
      audioBuffer = audioData;
      dataSize = audioData.length * 4; // 4 bytes per float
    }

    // Calculate RMS amplitude
    const rmsAmplitude = calculateRMS(audioBuffer);
    
    // Convert RMS to dB
    const amplitudeDb = rmsAmplitude > 0 ? 20 * Math.log10(rmsAmplitude) : -Infinity;
    
    // Detect silence (threshold: -40dB)
    const silenceDetected = amplitudeDb < -40;
    
    // Calculate dominant frequency using simple peak detection
    const frequencyHz = calculateDominantFrequency(audioBuffer, sampleRate);
    
    // Calculate average amplitude
    const averageAmplitude = calculateAverageAmplitude(audioBuffer);
    
    // Find peak frequency using FFT-like analysis (simplified)
    const peakFrequency = findPeakFrequency(audioBuffer, sampleRate);

    return {
      frequencyHz,
      amplitudeDb: Math.round(amplitudeDb * 10) / 10, // Round to 1 decimal
      audioDataSize: dataSize,
      sampleRate,
      channels,
      silenceDetected,
      peakFrequency,
      averageAmplitude: Math.round(averageAmplitude * 1000) / 1000,
      rmsAmplitude: Math.round(rmsAmplitude * 1000) / 1000
    };
  } catch (error) {
    console.error('Error analyzing audio data:', error);
    
    // Return default values on error
    return {
      frequencyHz: 0,
      amplitudeDb: -Infinity,
      audioDataSize: typeof audioData === 'string' ? audioData.length : 0,
      sampleRate,
      channels,
      silenceDetected: true,
      peakFrequency: 0,
      averageAmplitude: 0,
      rmsAmplitude: 0
    };
  }
}

/**
 * Calculate RMS (Root Mean Square) amplitude
 */
function calculateRMS(audioBuffer: Float32Array): number {
  let sum = 0;
  for (let i = 0; i < audioBuffer.length; i++) {
    sum += audioBuffer[i] * audioBuffer[i];
  }
  return Math.sqrt(sum / audioBuffer.length);
}

/**
 * Calculate average amplitude (absolute values)
 */
function calculateAverageAmplitude(audioBuffer: Float32Array): number {
  let sum = 0;
  for (let i = 0; i < audioBuffer.length; i++) {
    sum += Math.abs(audioBuffer[i]);
  }
  return sum / audioBuffer.length;
}

/**
 * Calculate dominant frequency using zero-crossing rate and autocorrelation
 */
function calculateDominantFrequency(audioBuffer: Float32Array, sampleRate: number): number {
  try {
    // Use autocorrelation to find the fundamental frequency
    const autocorrelation = autoCorrelate(audioBuffer, sampleRate);
    return autocorrelation;
  } catch (error) {
    console.error('Error calculating dominant frequency:', error);
    return 0;
  }
}

/**
 * Simple autocorrelation-based pitch detection
 */
function autoCorrelate(buffer: Float32Array, sampleRate: number): number {
  const SIZE = buffer.length;
  const MAX_SAMPLES = Math.floor(SIZE / 2);
  let bestOffset = -1;
  let bestCorrelation = 0;
  let rms = 0;
  let foundGoodCorrelation = false;
  const correlations = new Array(MAX_SAMPLES);

  // Calculate RMS
  for (let i = 0; i < SIZE; i++) {
    const val = buffer[i];
    rms += val * val;
  }
  rms = Math.sqrt(rms / SIZE);
  
  // Not enough signal
  if (rms < 0.01) return 0;

  let lastCorrelation = 1;
  for (let offset = 1; offset < MAX_SAMPLES; offset++) {
    let correlation = 0;

    for (let i = 0; i < MAX_SAMPLES; i++) {
      correlation += Math.abs((buffer[i]) - (buffer[i + offset]));
    }
    correlation = 1 - (correlation / MAX_SAMPLES);
    correlations[offset] = correlation;

    if (correlation > 0.9 && correlation > lastCorrelation) {
      foundGoodCorrelation = true;
      if (correlation > bestCorrelation) {
        bestCorrelation = correlation;
        bestOffset = offset;
      }
    } else if (foundGoodCorrelation) {
      const shift = (correlations[bestOffset + 1] - correlations[bestOffset - 1]) / correlations[bestOffset];
      return sampleRate / (bestOffset + (8 * shift));
    }
    lastCorrelation = correlation;
  }
  
  if (bestCorrelation > 0.01) {
    return sampleRate / bestOffset;
  }
  return 0;
}

/**
 * Find peak frequency using simplified FFT analysis
 */
function findPeakFrequency(audioBuffer: Float32Array, sampleRate: number): number {
  try {
    // Simple frequency analysis using zero-crossing rate
    let crossings = 0;
    for (let i = 1; i < audioBuffer.length; i++) {
      if ((audioBuffer[i] >= 0) !== (audioBuffer[i - 1] >= 0)) {
        crossings++;
      }
    }
    
    // Estimate frequency from zero crossings
    const duration = audioBuffer.length / sampleRate;
    const frequency = (crossings / 2) / duration;
    
    return Math.round(frequency);
  } catch (error) {
    console.error('Error finding peak frequency:', error);
    return 0;
  }
}

/**
 * Analyze microphone input audio for real-time monitoring
 */
export function analyzeMicrophoneAudio(
  inputBuffer: Float32Array,
  sampleRate: number = 24000
): AudioAnalysisResult {
  return analyzeAudioData(inputBuffer, sampleRate, 1);
}

/**
 * Analyze assistant audio output for playback monitoring
 */
export function analyzeAssistantAudio(
  base64AudioData: string,
  sampleRate: number = 24000
): AudioAnalysisResult {
  return analyzeAudioData(base64AudioData, sampleRate, 1);
}
