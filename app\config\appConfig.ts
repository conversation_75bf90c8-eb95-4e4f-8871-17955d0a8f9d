/**
 * Application Configuration
 *
 * This file contains all the important configuration values and constants used throughout the application.
 * Centralizing these values makes it easier to maintain and update the application.
 */

// Environment variables validation (production-ready)

// Helper function to remove trailing slashes
const removeTrailingSlash = (url: string) => url.replace(/\/+$/, '');

// Helper function to ensure URL is provided and properly formatted
const ensureUrl = (url: string | undefined, type: string): string => {
  if (!url) {
    // During build process or in development, use placeholder values
    if (process.env.NODE_ENV === 'development' || process.env.DOCKER_BUILD === 'true') {
      return type === 'API' ? 'http://localhost:8000' : 'ws://localhost:8000';
    }

    // In production, check if we have window variables (from static/env-config.js)
    if (typeof window !== 'undefined') {
      const windowVar = type === 'API' ? (window as any).API_BASE_URL : (window as any).WS_BASE_URL;
      if (windowVar) {
        return windowVar;
      }
    }

    // If we still don't have a URL, log a warning but don't throw an error
    const errorMessage = `${type} URL is not set. Please set the NEXT_PUBLIC_${type === 'API' ? 'API_URL' : 'WS_URL'} environment variable.`;
    console.error(`ERROR: ${errorMessage}`);
    return type === 'API' ? 'http://localhost:8000' : 'ws://localhost:8000';
  }
  return removeTrailingSlash(url);
};

// API Base URL (from environment variable, trailing slash removed)
export const API_BASE_URL = ensureUrl(process.env.NEXT_PUBLIC_API_URL, 'API');

// WebSocket Base URL (from environment variable, trailing slash removed)
// Ensure it uses the correct protocol (ws:// or wss://)
export const WS_BASE_URL = (() => {
  const url = ensureUrl(process.env.NEXT_PUBLIC_WS_URL, 'WS');
  // Convert http:// to ws:// and https:// to wss:// if needed
  if (url.startsWith('http://')) {
    return url.replace('http://', 'ws://');
  } else if (url.startsWith('https://')) {
    return url.replace('https://', 'wss://');
  }
  return url;
})();

// WebSocket Configuration
export const WS_CONFIG = {
  BASE_URL: WS_BASE_URL, // Use the properly formatted WS_BASE_URL
  RECONNECT_ATTEMPTS: 3,
  RECONNECT_DELAY: 800, // Reduced from 1500ms to 800ms
  FINAL_RECONNECT_DELAY: 2000, // Reduced from 5000ms to 2000ms
  CONNECTION_TIMEOUT: 5000, // Reduced from 10000ms to 5000ms
};

// Audio Configuration
export const AUDIO_CONFIG = {
  SAMPLE_RATE: 24000,
  CHANNELS: 1,
  PROCESSOR_BUFFER_SIZE: 2048,
  INIT_RETRY_DELAY: 100, // Reduced from 200ms to 100ms
  PLAYBACK_DELAY: 0, // 0ms (immediate playback)
  AUTO_START_RECORDING: true, // Automatically start recording when connection is established
};

// Agent Role Configuration
export const AGENT_ROLES = {
  // Standardized configuration for all agent roles
  CONNECTION_DELAY: 0, // Reduced to 0ms (no delay)
  RECORDING_DELAY: 0, // Reduced to 0ms (no delay)
  RETRY_DELAY: 800, // Reduced from 1500ms to 800ms
};

// Timer Configuration
export const TIMER_CONFIG = {
  DEFAULT_TIME: 900, // 15 minutes in seconds (maximum allowed time)
  MAX_TIME: 900,     // Maximum allowed time in seconds (15 minutes)
  UPDATE_INTERVAL: 1000, // 1 second
};

// Error Messages
export const ERROR_MESSAGES = {
  CONNECTION: {
    DEFAULT: 'Connection error - please try again',
    PPCA: 'Personal assistant connection error - please try again',
    MAX_ATTEMPTS: 'Connection failed after multiple attempts',
  },
  RECORDING: {
    DEFAULT: 'Failed to start recording. Please refresh the page and try again.',
    PPCA: 'Failed to start PPCA recording. Please try again or refresh the page.',
  },
  AUDIO: {
    INIT_FAILED: 'Audio initialization failed',
  },
};

// User Interface Configuration
export const UI_CONFIG = {
  AVATAR: {
    INACTIVE_OPACITY: 0.4, // 40% opacity for inactive avatars
    ACTIVE_OPACITY: 1.0, // 100% opacity for active avatars
    SIZE: {
      DEFAULT: '44', // Default avatar size in pixels
      LARGE: '64', // Large avatar size in pixels
    },
  },
  ANIMATION: {
    TRANSITION_DURATION: 300, // 300ms
  },
};

// Local Storage Keys
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_DETAILS: 'userDetails',
};

// API Endpoints
export const API_ENDPOINTS = {
  USER: {
    ME: '/users/me',
    UPDATE_STAGE: '/users/stage',
  },
};

// Configuration loaded successfully
